import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibration/vibration.dart';
import 'package:intl/intl.dart';

class ForumsScreen extends StatefulWidget {
  const ForumsScreen({super.key});

  @override
  State<ForumsScreen> createState() => _ForumsScreenState();
}

class _ForumsScreenState extends State<ForumsScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  final List<ChatRoom> _chatRooms = [];
  late AnimationController _fabAnimationController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _loadChatRooms();
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _loadChatRooms() {
    setState(() {
      _chatRooms.addAll([
        ChatRoom(
          id: '1',
          name: 'General Discussion',
          description: 'Talk about anything drive-related',
          lastMessage: 'Anyone know good routes to avoid traffic?',
          lastMessageTime: DateTime.now().subtract(const Duration(minutes: 5)),
          memberCount: 1247,
          isOnline: true,
          unreadCount: 3,
          avatar: Icons.chat_rounded,
          color: Colors.blue,
        ),
        ChatRoom(
          id: '2',
          name: 'Driver Tips & Tricks',
          description: 'Share your driving expertise',
          lastMessage: 'Great fuel-saving tip! Thanks for sharing.',
          lastMessageTime: DateTime.now().subtract(const Duration(minutes: 15)),
          memberCount: 892,
          isOnline: true,
          unreadCount: 0,
          avatar: Icons.lightbulb_rounded,
          color: Colors.amber,
        ),
        ChatRoom(
          id: '3',
          name: 'Job Opportunities',
          description: 'Find and share job opportunities',
          lastMessage: 'New delivery route available downtown',
          lastMessageTime: DateTime.now().subtract(const Duration(hours: 1)),
          memberCount: 2156,
          isOnline: false,
          unreadCount: 7,
          avatar: Icons.work_rounded,
          color: Colors.green,
        ),
        ChatRoom(
          id: '4',
          name: 'Vehicle Maintenance',
          description: 'Car care and maintenance advice',
          lastMessage: 'Where to get best oil change deals?',
          lastMessageTime: DateTime.now().subtract(const Duration(hours: 2)),
          memberCount: 634,
          isOnline: true,
          unreadCount: 1,
          avatar: Icons.build_rounded,
          color: Colors.orange,
        ),
      ]);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final ColorScheme scheme = Theme.of(context).colorScheme;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSearchBar(scheme),
                  const SizedBox(height: 20),
                  _buildActiveNowSection(scheme),
                  const SizedBox(height: 24),
                  Text(
                    'Chat Rooms',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildChatRoomCard(_chatRooms[index]),
              childCount: _chatRooms.length,
            ),
          ),
        ],
      ),
      floatingActionButton: ScaleTransition(
        scale: Tween<double>(begin: 0, end: 1).animate(
          CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeOut),
        ),
        child: FloatingActionButton.extended(
          backgroundColor: scheme.primary,
          onPressed: () {
            HapticFeedback.mediumImpact();
            Vibration.vibrate(duration: 100);
            _showCreateRoomDialog();
          },
          icon: Icon(Icons.add_rounded, color: scheme.onPrimary),
          label: Text(
            'New Room',
            style: TextStyle(color: scheme.onPrimary, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar(ColorScheme scheme) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            scheme.primaryContainer.withValues(alpha: 0.3),
            scheme.secondaryContainer.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search rooms or messages...',
          prefixIcon: Icon(Icons.search_rounded, color: scheme.primary),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
        onTap: () {
          HapticFeedback.lightImpact();
        },
      ),
    );
  }

  Widget _buildActiveNowSection(ColorScheme scheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Active Now',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 8,
            itemBuilder: (context, index) => _buildActiveUser(index, scheme),
          ),
        ),
      ],
    );
  }

  Widget _buildActiveUser(int index, ColorScheme scheme) {
    final names = ['Alex', 'Sarah', 'Mike', 'Emma', 'John', 'Lisa', 'Tom', 'Amy'];
    
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [scheme.primary, scheme.primaryContainer],
                  ),
                ),
                child: Icon(
                  Icons.person,
                  color: scheme.onPrimary,
                  size: 24,
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            names[index],
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatRoomCard(ChatRoom room) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            HapticFeedback.lightImpact();
            _openChatRoom(room);
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: scheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: scheme.outlineVariant.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                _buildRoomAvatar(room, scheme),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildRoomInfo(room, scheme),
                ),
                _buildRoomMeta(room, scheme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRoomAvatar(ChatRoom room, ColorScheme scheme) {
    return Stack(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: room.color.withValues(alpha: 0.2),
          ),
          child: Icon(
            room.avatar,
            color: room.color,
            size: 28,
          ),
        ),
        if (room.isOnline)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 18,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRoomInfo(ChatRoom room, ColorScheme scheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                room.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              '${room.memberCount}',
              style: TextStyle(
                fontSize: 12,
                color: scheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.people_rounded,
              size: 14,
              color: scheme.onSurfaceVariant,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          room.description,
          style: TextStyle(
            fontSize: 12,
            color: scheme.onSurfaceVariant,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 6),
        Text(
          room.lastMessage,
          style: TextStyle(
            fontSize: 14,
            color: room.unreadCount > 0 
                ? scheme.onSurface 
                : scheme.onSurfaceVariant,
            fontWeight: room.unreadCount > 0 
                ? FontWeight.w500 
                : FontWeight.w400,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
      ],
    );
  }

  Widget _buildRoomMeta(ChatRoom room, ColorScheme scheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          DateFormat('HH:mm').format(room.lastMessageTime),
          style: TextStyle(
            fontSize: 12,
            color: scheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 8),
        if (room.unreadCount > 0)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: scheme.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              room.unreadCount.toString(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: scheme.onPrimary,
              ),
            ),
          ),
      ],
    );
  }

  void _openChatRoom(ChatRoom room) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => 
            ChatRoomScreen(room: room),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }

  void _showCreateRoomDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Room'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Room Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }
}

class ChatRoomScreen extends StatefulWidget {
  final ChatRoom room;

  const ChatRoomScreen({super.key, required this.room});

  @override
  State<ChatRoomScreen> createState() => _ChatRoomScreenState();
}

class _ChatRoomScreenState extends State<ChatRoomScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  void _loadMessages() {
    setState(() {
      _messages.addAll([
        ChatMessage(
          id: '1',
          text: 'Welcome to ${widget.room.name}!',
          sender: 'System',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
          isMe: false,
        ),
        ChatMessage(
          id: '2',
          text: 'Hey everyone! Any good routes to avoid the downtown construction?',
          sender: 'Alex',
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
          isMe: false,
        ),
        ChatMessage(
          id: '3',
          text: 'Try taking the highway bypass, it saves about 15 minutes',
          sender: 'You',
          timestamp: DateTime.now().subtract(const Duration(minutes: 25)),
          isMe: true,
        ),
      ]);
    });
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme scheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.room.name,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            Text(
              '${widget.room.memberCount} members',
              style: TextStyle(
                fontSize: 12,
                color: scheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        backgroundColor: scheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.videocam_rounded),
            onPressed: () {
              HapticFeedback.lightImpact();
            },
          ),
          IconButton(
            icon: const Icon(Icons.call_rounded),
            onPressed: () {
              HapticFeedback.lightImpact();
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert_rounded),
            onPressed: () {
              HapticFeedback.lightImpact();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) => _buildMessageBubble(_messages[index]),
            ),
          ),
          _buildMessageInput(scheme),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: message.isMe 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        children: [
          if (!message.isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: scheme.primaryContainer,
              child: Text(
                message.sender[0],
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: scheme.onPrimaryContainer,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isMe 
                    ? scheme.primary 
                    : scheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!message.isMe)
                    Text(
                      message.sender,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: scheme.primary,
                      ),
                    ),
                  Text(
                    message.text,
                    style: TextStyle(
                      color: message.isMe 
                          ? scheme.onPrimary 
                          : scheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('HH:mm').format(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: message.isMe 
                          ? scheme.onPrimary.withValues(alpha: 0.7)
                          : scheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput(ColorScheme scheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: scheme.surface,
        border: Border(
          top: BorderSide(color: scheme.outlineVariant),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(Icons.attach_file_rounded, color: scheme.primary),
            onPressed: () {
              HapticFeedback.lightImpact();
            },
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: scheme.surfaceContainerHighest,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: Icon(Icons.mic_rounded, color: scheme.primary),
            onPressed: () {
              HapticFeedback.lightImpact();
              // Voice recording functionality
            },
          ),
          IconButton(
            icon: Icon(Icons.send_rounded, color: scheme.primary),
            onPressed: _sendMessage,
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      HapticFeedback.lightImpact();
      setState(() {
        _messages.add(
          ChatMessage(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            text: _messageController.text,
            sender: 'You',
            timestamp: DateTime.now(),
            isMe: true,
          ),
        );
      });
      _messageController.clear();
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }
}

class ChatRoom {
  final String id;
  final String name;
  final String description;
  final String lastMessage;
  final DateTime lastMessageTime;
  final int memberCount;
  final bool isOnline;
  final int unreadCount;
  final IconData avatar;
  final Color color;

  ChatRoom({
    required this.id,
    required this.name,
    required this.description,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.memberCount,
    required this.isOnline,
    required this.unreadCount,
    required this.avatar,
    required this.color,
  });
}

class ChatMessage {
  final String id;
  final String text;
  final String sender;
  final DateTime timestamp;
  final bool isMe;

  ChatMessage({
    required this.id,
    required this.text,
    required this.sender,
    required this.timestamp,
    required this.isMe,
  });
}

