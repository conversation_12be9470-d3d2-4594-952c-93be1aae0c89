import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibration/vibration.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with AutomaticKeepAliveClientMixin {
  bool _notifications = true;
  bool _locationServices = true;
  bool _hapticFeedback = true;
  bool _soundEffects = true;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final ColorScheme scheme = Theme.of(context).colorScheme;
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileSection(scheme),
            const SizedBox(height: 32),
            _buildPartnerSection(scheme),
            const SizedBox(height: 32),
            _buildSettingsSections(scheme, isDark),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection(ColorScheme scheme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            scheme.primaryContainer.withValues(alpha: 0.8),
            scheme.primary.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: scheme.onPrimary.withValues(alpha: 0.2),
              border: Border.all(
                color: scheme.onPrimary.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Icon(
              Icons.person_rounded,
              size: 40,
              color: scheme.onPrimary,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'John Doe',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: scheme.onPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '<EMAIL>',
                  style: TextStyle(
                    fontSize: 16,
                    color: scheme.onPrimary.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: scheme.onPrimary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Premium Member',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: scheme.onPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.edit_rounded,
              color: scheme.onPrimary,
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              _showEditProfileDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPartnerSection(ColorScheme scheme) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.withValues(alpha: 0.8),
            Colors.deepOrange.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            HapticFeedback.mediumImpact();
            Vibration.vibrate(duration: 100);
            _showPartnerRegistrationDialog();
          },
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.business_center_rounded,
                    size: 32,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Become a Partner',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Join our partner program and grow your business with us',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsSections(ColorScheme scheme, bool isDark) {
    return Column(
      children: [
        _buildSettingsSection(
          'Preferences',
          [
            _buildSwitchTile(
              'Dark Mode',
              'Use dark theme',
              Icons.dark_mode_rounded,
              isDark,
              (value) {
                HapticFeedback.lightImpact();
                // Toggle theme mode
              },
              scheme,
            ),
            _buildSwitchTile(
              'Notifications',
              'Receive push notifications',
              Icons.notifications_rounded,
              _notifications,
              (value) {
                HapticFeedback.lightImpact();
                setState(() => _notifications = value);
              },
              scheme,
            ),
            _buildSwitchTile(
              'Location Services',
              'Allow location tracking',
              Icons.location_on_rounded,
              _locationServices,
              (value) {
                HapticFeedback.lightImpact();
                setState(() => _locationServices = value);
              },
              scheme,
            ),
          ],
          scheme,
        ),
        const SizedBox(height: 24),
        _buildSettingsSection(
          'Experience',
          [
            _buildSwitchTile(
              'Haptic Feedback',
              'Vibration on interactions',
              Icons.vibration_rounded,
              _hapticFeedback,
              (value) {
                HapticFeedback.lightImpact();
                setState(() => _hapticFeedback = value);
              },
              scheme,
            ),
            _buildSwitchTile(
              'Sound Effects',
              'Audio feedback',
              Icons.volume_up_rounded,
              _soundEffects,
              (value) {
                HapticFeedback.lightImpact();
                setState(() => _soundEffects = value);
              },
              scheme,
            ),
          ],
          scheme,
        ),
        const SizedBox(height: 24),
        _buildSettingsSection(
          'Account',
          [
            _buildActionTile(
              'Privacy & Security',
              'Manage your privacy settings',
              Icons.shield_rounded,
              () {
                HapticFeedback.lightImpact();
                _showPrivacySettings();
              },
              scheme,
            ),
            _buildActionTile(
              'Payment Methods',
              'Manage cards and payments',
              Icons.payment_rounded,
              () {
                HapticFeedback.lightImpact();
                _showPaymentMethods();
              },
              scheme,
            ),
            _buildActionTile(
              'Trip History',
              'View your past trips',
              Icons.history_rounded,
              () {
                HapticFeedback.lightImpact();
                _showTripHistory();
              },
              scheme,
            ),
          ],
          scheme,
        ),
        const SizedBox(height: 24),
        _buildSettingsSection(
          'Support',
          [
            _buildActionTile(
              'Help & Support',
              'Get help or contact us',
              Icons.help_rounded,
              () {
                HapticFeedback.lightImpact();
                _showHelpSupport();
              },
              scheme,
            ),
            _buildActionTile(
              'Terms & Conditions',
              'Read our terms of service',
              Icons.description_rounded,
              () {
                HapticFeedback.lightImpact();
                _showTermsConditions();
              },
              scheme,
            ),
            _buildActionTile(
              'About Drive-On',
              'App version and info',
              Icons.info_rounded,
              () {
                HapticFeedback.lightImpact();
                _showAboutDialog();
              },
              scheme,
            ),
          ],
          scheme,
        ),
        const SizedBox(height: 32),
        _buildSignOutButton(scheme),
      ],
    );
  }

  Widget _buildSettingsSection(
    String title, 
    List<Widget> children, 
    ColorScheme scheme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: scheme.onSurface,
            ),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: scheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: scheme.outlineVariant.withValues(alpha: 0.5),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
    ColorScheme scheme,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: scheme.primaryContainer.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: scheme.primary, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: scheme.primary,
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
    ColorScheme scheme,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: scheme.primaryContainer.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: scheme.primary, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(subtitle),
      trailing: Icon(
        Icons.arrow_forward_ios_rounded,
        color: scheme.onSurfaceVariant,
        size: 16,
      ),
      onTap: onTap,
    );
  }

  Widget _buildSignOutButton(ColorScheme scheme) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            HapticFeedback.mediumImpact();
            Vibration.vibrate(duration: 100);
            _showSignOutDialog();
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.logout_rounded, color: Colors.red),
                const SizedBox(width: 12),
                Text(
                  'Sign Out',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showEditProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: const Text('Profile editing form would appear here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showPartnerRegistrationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Partner Registration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Join our partner program and enjoy:'),
            const SizedBox(height: 12),
            const Text('• Priority job assignments'),
            const Text('• Higher earnings potential'),
            const Text('• Exclusive partner benefits'),
            const Text('• 24/7 dedicated support'),
            const SizedBox(height: 16),
            const Text('Ready to get started?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showPartnerForm();
            },
            child: const Text('Register Now'),
          ),
        ],
      ),
    );
  }

  void _showPartnerForm() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Partner Application'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Business Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Business Type',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Years of Experience',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Partner application submitted!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  void _showPrivacySettings() {
    _showFeatureDialog('Privacy & Security Settings');
  }

  void _showPaymentMethods() {
    _showFeatureDialog('Payment Methods');
  }

  void _showTripHistory() {
    _showFeatureDialog('Trip History');
  }

  void _showHelpSupport() {
    _showFeatureDialog('Help & Support');
  }

  void _showTermsConditions() {
    _showFeatureDialog('Terms & Conditions');
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'Drive-On',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.directions_car_rounded,
          color: Theme.of(context).colorScheme.onPrimary,
        ),
      ),
      children: [
        const Text('Your premium driving companion app.'),
      ],
    );
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushReplacementNamed(context, '/login');
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  void _showFeatureDialog(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature),
        content: Text('$feature screen would appear here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
