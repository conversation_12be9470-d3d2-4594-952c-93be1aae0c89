import 'dart:math' as math;

import 'package:flutter/material.dart';

class WaveBackground extends StatefulWidget {
  const WaveBackground({
    super.key,
    this.heightFactor = 0.45,
    this.amplitude = 20,
    this.speed = 1.0,
    this.colors,
    this.child,
  });

  final double heightFactor;
  final double amplitude;
  final double speed;
  final List<Color>? colors;
  final Widget? child;

  @override
  State<WaveBackground> createState() => _WaveBackgroundState();
}

class _WaveBackgroundState extends State<WaveBackground>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    final List<Color> gradientColors = widget.colors ?? <Color>[
      scheme.primaryContainer.withValues(alpha: 0.90),
      scheme.primary.withValues(alpha: 0.90),
    ];

    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final double height = constraints.maxHeight * widget.heightFactor;
        return Stack(
          children: <Widget>[
            SizedBox(
              width: constraints.maxWidth,
              height: height,
              child: AnimatedBuilder(
                animation: _controller,
                builder: (BuildContext context, Widget? _) {
                  return CustomPaint(
                    painter: _WavePainter(
                      progress: _controller.value * 2 * math.pi * widget.speed,
                      amplitude: widget.amplitude,
                      colors: gradientColors,
                    ),
                  );
                },
              ),
            ),
            if (widget.child != null)
              Positioned.fill(
                child: Align(
                  alignment: Alignment.topCenter,
                  child: SizedBox(
                    height: height,
                    child: widget.child,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class _WavePainter extends CustomPainter {
  _WavePainter({
    required this.progress,
    required this.amplitude,
    required this.colors,
  });

  final double progress;
  final double amplitude;
  final List<Color> colors;

  @override
  void paint(Canvas canvas, Size size) {
    final Rect rect = Offset.zero & size;
    final Paint paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: colors,
      ).createShader(rect);

    final Path path = Path()..moveTo(0, size.height);

    // Draw multiple layered waves for depth
    for (int i = 0; i < 2; i++) {
      final double localAmplitude = amplitude * (1 - i * 0.35);
      final double phaseShift = progress * (1 + i * 0.2) + i * math.pi / 2;
      path.reset();
      path.moveTo(0, size.height);
      for (double x = 0; x <= size.width; x++) {
        final double y = size.height -
            (math.sin((x / size.width * 2 * math.pi) + phaseShift) *
                    localAmplitude +
                localAmplitude * 1.4);
        path.lineTo(x, y);
      }
      path.lineTo(size.width, size.height);
      path.close();
      canvas.drawPath(path, paint);
      paint.blendMode = BlendMode.srcOver;
    }
  }

  @override
  bool shouldRepaint(covariant _WavePainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.amplitude != amplitude ||
        oldDelegate.colors != colors;
  }
}





