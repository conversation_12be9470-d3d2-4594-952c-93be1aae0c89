import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shimmer/shimmer.dart';
import 'package:blur/blur.dart';
import 'package:vibration/vibration.dart';
import 'package:intl/intl.dart';

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  final List<NewsArticle> _articles = [];
  String _selectedCategory = 'All';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadNews();
  }

  Future<void> _loadNews() async {
    await Future.delayed(const Duration(seconds: 2));
    
    setState(() {
      _articles.addAll([
        NewsArticle(
          id: '1',
          title: 'New Electric Vehicle Incentives Announced',
          summary: 'Government announces new incentives for electric vehicle adoption in the transportation sector.',
          content: 'Full article content would go here...',
          imageUrl: 'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=400&h=200&fit=crop',
          publishedAt: DateTime.now().subtract(const Duration(hours: 2)),
          category: 'Policy',
          author: 'Transport News',
          readTime: '3 min read',
          isBookmarked: false,
          viewCount: 1250,
        ),
        NewsArticle(
          id: '2',
          title: 'Safety First: New Traffic Regulations in Effect',
          summary: 'Important updates to traffic regulations that all drivers should know about.',
          content: 'Full article content would go here...',
          imageUrl: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=200&fit=crop',
          publishedAt: DateTime.now().subtract(const Duration(hours: 5)),
          category: 'Safety',
          author: 'Safety Bureau',
          readTime: '5 min read',
          isBookmarked: true,
          viewCount: 2100,
        ),
        NewsArticle(
          id: '3',
          title: 'Fuel Prices Expected to Drop Next Month',
          summary: 'Economic analysts predict a significant decrease in fuel prices across major cities.',
          content: 'Full article content would go here...',
          imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop',
          publishedAt: DateTime.now().subtract(const Duration(days: 1)),
          category: 'Economy',
          author: 'Economic Times',
          readTime: '4 min read',
          isBookmarked: false,
          viewCount: 3400,
        ),
        NewsArticle(
          id: '4',
          title: 'Smart Traffic Management System Launch',
          summary: 'City introduces AI-powered traffic management to reduce congestion.',
          content: 'Full article content would go here...',
          imageUrl: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=200&fit=crop',
          publishedAt: DateTime.now().subtract(const Duration(days: 2)),
          category: 'Technology',
          author: 'Tech Today',
          readTime: '6 min read',
          isBookmarked: false,
          viewCount: 1800,
        ),
      ]);
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final ColorScheme scheme = Theme.of(context).colorScheme;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadNews,
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSearchBar(scheme),
                    const SizedBox(height: 20),
                    _buildCategoryFilters(scheme),
                    const SizedBox(height: 24),
                    Text(
                      'Latest News',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
            _isLoading ? _buildShimmerList() : _buildNewsList(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Theme.of(context).colorScheme.primary,
        onPressed: () {
          HapticFeedback.mediumImpact();
          Vibration.vibrate(duration: 100);
          _showSubmitNewsDialog();
        },
        child: Icon(
          Icons.add_rounded,
          color: Theme.of(context).colorScheme.onPrimary,
        ),
      ),
    );
  }

  Widget _buildSearchBar(ColorScheme scheme) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            scheme.primaryContainer.withValues(alpha: 0.3),
            scheme.secondaryContainer.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search news...',
          prefixIcon: Icon(Icons.search_rounded, color: scheme.primary),
          suffixIcon: Icon(Icons.mic_rounded, color: scheme.primary),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
        onTap: () {
          HapticFeedback.lightImpact();
        },
      ),
    );
  }

  Widget _buildCategoryFilters(ColorScheme scheme) {
    final categories = ['All', 'Policy', 'Safety', 'Economy', 'Technology'];
    
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category;
          
          return Container(
            margin: EdgeInsets.only(right: index < categories.length - 1 ? 12 : 0),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                HapticFeedback.lightImpact();
                setState(() {
                  _selectedCategory = category;
                });
              },
              backgroundColor: scheme.surfaceContainerHighest,
              selectedColor: scheme.primaryContainer,
              labelStyle: TextStyle(
                color: isSelected ? scheme.onPrimaryContainer : scheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerList() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
        childCount: 5,
      ),
    );
  }

  Widget _buildNewsList() {
    final filteredArticles = _selectedCategory == 'All' 
        ? _articles 
        : _articles.where((article) => article.category == _selectedCategory).toList();

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == 0 && filteredArticles.isNotEmpty) {
            return _buildFeaturedArticle(filteredArticles[0]);
          }
          return _buildNewsCard(filteredArticles[index]);
        },
        childCount: filteredArticles.length,
      ),
    );
  }

  Widget _buildFeaturedArticle(NewsArticle article) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      height: 280,
      child: Blur(
        blur: 10,
        blurColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                scheme.primaryContainer.withValues(alpha: 0.9),
                scheme.primary.withValues(alpha: 0.9),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                HapticFeedback.lightImpact();
                _openArticle(article);
              },
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: scheme.onPrimary.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            'FEATURED',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              color: scheme.onPrimary,
                            ),
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          icon: Icon(
                            article.isBookmarked 
                                ? Icons.bookmark_rounded
                                : Icons.bookmark_border_rounded,
                            color: scheme.onPrimary,
                          ),
                          onPressed: () {
                            HapticFeedback.lightImpact();
                            _toggleBookmark(article);
                          },
                        ),
                      ],
                    ),
                    const Spacer(),
                    Text(
                      article.title,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: scheme.onPrimary,
                        height: 1.2,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      article.summary,
                      style: TextStyle(
                        fontSize: 16,
                        color: scheme.onPrimary.withValues(alpha: 0.9),
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Text(
                          article.author,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: scheme.onPrimary.withValues(alpha: 0.9),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          DateFormat('MMM d').format(article.publishedAt),
                          style: TextStyle(
                            fontSize: 14,
                            color: scheme.onPrimary.withValues(alpha: 0.8),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          article.readTime,
                          style: TextStyle(
                            fontSize: 14,
                            color: scheme.onPrimary.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNewsCard(NewsArticle article) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Blur(
        blur: 10,
        blurColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            color: scheme.surface.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: scheme.outlineVariant.withValues(alpha: 0.5),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                HapticFeedback.lightImpact();
                _openArticle(article);
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          colors: [
                            _getCategoryColor(article.category),
                            _getCategoryColor(article.category).withValues(alpha: 0.7),
                          ],
                        ),
                      ),
                      child: Icon(
                        _getCategoryIcon(article.category),
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: _getCategoryColor(article.category).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  article.category,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: _getCategoryColor(article.category),
                                  ),
                                ),
                              ),
                              const Spacer(),
                              IconButton(
                                icon: Icon(
                                  article.isBookmarked 
                                      ? Icons.bookmark_rounded
                                      : Icons.bookmark_border_rounded,
                                  color: scheme.onSurfaceVariant,
                                  size: 20,
                                ),
                                onPressed: () {
                                  HapticFeedback.lightImpact();
                                  _toggleBookmark(article);
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            article.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              height: 1.3,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.visibility_rounded, size: 14, color: scheme.onSurfaceVariant),
                              const SizedBox(width: 4),
                              Text(
                                '${article.viewCount}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: scheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                DateFormat('MMM d').format(article.publishedAt),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: scheme.onSurfaceVariant,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                article.readTime,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: scheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Policy': return Colors.blue;
      case 'Safety': return Colors.red;
      case 'Economy': return Colors.green;
      case 'Technology': return Colors.purple;
      default: return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Policy': return Icons.policy_rounded;
      case 'Safety': return Icons.security_rounded;
      case 'Economy': return Icons.trending_up_rounded;
      case 'Technology': return Icons.computer_rounded;
      default: return Icons.article_rounded;
    }
  }

  void _toggleBookmark(NewsArticle article) {
    setState(() {
      article.isBookmarked = !article.isBookmarked;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          article.isBookmarked ? 'Article bookmarked' : 'Bookmark removed',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _openArticle(NewsArticle article) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => 
            ArticleScreen(article: article),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }

  void _showSubmitNewsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Submit News'),
        content: const Text('News submission form would appear here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }
}

class ArticleScreen extends StatelessWidget {
  final NewsArticle article;

  const ArticleScreen({super.key, required this.article});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Article'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              article.title,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'By ${article.author} • ${DateFormat('MMM d, yyyy').format(article.publishedAt)}',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              article.content,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }
}

class NewsArticle {
  final String id;
  final String title;
  final String summary;
  final String content;
  final String imageUrl;
  final DateTime publishedAt;
  final String category;
  final String author;
  final String readTime;
  bool isBookmarked;
  final int viewCount;

  NewsArticle({
    required this.id,
    required this.title,
    required this.summary,
    required this.content,
    required this.imageUrl,
    required this.publishedAt,
    required this.category,
    required this.author,
    required this.readTime,
    required this.isBookmarked,
    required this.viewCount,
  });
}

