import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shimmer/shimmer.dart';
import 'package:blur/blur.dart';
import 'package:vibration/vibration.dart';
import 'package:intl/intl.dart';

class JobsScreen extends StatefulWidget {
  const JobsScreen({super.key});

  @override
  State<JobsScreen> createState() => _JobsScreenState();
}

class _JobsScreenState extends State<JobsScreen>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  final List<Job> _jobs = [];
  String _selectedFilter = 'All';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadJobs();
  }

  Future<void> _loadJobs() async {
    await Future.delayed(const Duration(seconds: 2));
    
    setState(() {
      _jobs.addAll([
        Job(
          id: '1',
          title: 'Airport Pickup',
          description: 'Need pickup from downtown to airport',
          price: 45.00,
          distance: '12 km',
          duration: '25 min',
          pickupLocation: 'Downtown Plaza',
          dropLocation: 'International Airport',
          isUrgent: true,
          postedTime: DateTime.now().subtract(const Duration(minutes: 15)),
          category: 'Ride',
          clientRating: 4.8,
        ),
        Job(
          id: '2',
          title: 'Furniture Delivery',
          description: 'Deliver sofa from store to apartment',
          price: 80.00,
          distance: '8 km',
          duration: '45 min',
          pickupLocation: 'Furniture Store',
          dropLocation: 'Oak Street Apartments',
          isUrgent: false,
          postedTime: DateTime.now().subtract(const Duration(hours: 2)),
          category: 'Delivery',
          clientRating: 4.6,
        ),
        Job(
          id: '3',
          title: 'Food Delivery',
          description: 'Multiple restaurant pickups',
          price: 25.00,
          distance: '5 km',
          duration: '30 min',
          pickupLocation: 'Restaurant District',
          dropLocation: 'Residential Area',
          isUrgent: true,
          postedTime: DateTime.now().subtract(const Duration(minutes: 5)),
          category: 'Food',
          clientRating: 4.9,
        ),
      ]);
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final ColorScheme scheme = Theme.of(context).colorScheme;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadJobs,
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSearchBar(scheme),
                    const SizedBox(height: 20),
                    _buildPostJobButton(scheme),
                    const SizedBox(height: 20),
                    _buildFilterChips(scheme),
                    const SizedBox(height: 24),
                    Text(
                      'Available Jobs',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
            _isLoading ? _buildShimmerList() : _buildJobsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(ColorScheme scheme) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            scheme.primaryContainer.withValues(alpha: 0.3),
            scheme.secondaryContainer.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search jobs...',
          prefixIcon: Icon(Icons.search_rounded, color: scheme.primary),
          suffixIcon: Icon(Icons.tune_rounded, color: scheme.primary),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
        onTap: () {
          HapticFeedback.lightImpact();
        },
      ),
    );
  }

  Widget _buildPostJobButton(ColorScheme scheme) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [scheme.primary, scheme.primaryContainer],
        ),
        boxShadow: [
          BoxShadow(
            color: scheme.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            HapticFeedback.mediumImpact();
            Vibration.vibrate(duration: 100);
            _showPostJobDialog();
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: scheme.onPrimary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.add_task_rounded,
                    color: scheme.onPrimary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Post a Job',
                        style: TextStyle(
                          color: scheme.onPrimary,
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Find the perfect driver for your needs',
                        style: TextStyle(
                          color: scheme.onPrimary.withValues(alpha: 0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: scheme.onPrimary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChips(ColorScheme scheme) {
    final filters = ['All', 'Ride', 'Delivery', 'Food', 'Urgent'];
    
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter;
          
          return Container(
            margin: EdgeInsets.only(right: index < filters.length - 1 ? 12 : 0),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                HapticFeedback.lightImpact();
                setState(() {
                  _selectedFilter = filter;
                });
              },
              backgroundColor: scheme.surfaceContainerHighest,
              selectedColor: scheme.primaryContainer,
              labelStyle: TextStyle(
                color: isSelected ? scheme.onPrimaryContainer : scheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerList() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            height: 140,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
        childCount: 6,
      ),
    );
  }

  Widget _buildJobsList() {
    final filteredJobs = _selectedFilter == 'All' 
        ? _jobs 
        : _jobs.where((job) => job.category == _selectedFilter || 
                              (_selectedFilter == 'Urgent' && job.isUrgent)).toList();

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) => _buildJobCard(filteredJobs[index]),
        childCount: filteredJobs.length,
      ),
    );
  }

  Widget _buildJobCard(Job job) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Blur(
        blur: 10,
        blurColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            color: scheme.surface.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: job.isUrgent 
                  ? Colors.orange.withValues(alpha: 0.5)
                  : scheme.outlineVariant.withValues(alpha: 0.5),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                HapticFeedback.lightImpact();
                _showJobDetails(job);
              },
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildJobHeader(job, scheme),
                    const SizedBox(height: 12),
                    _buildJobDescription(job, scheme),
                    const SizedBox(height: 16),
                    _buildJobDetails(job, scheme),
                    const SizedBox(height: 16),
                    _buildJobFooter(job, scheme),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildJobHeader(Job job, ColorScheme scheme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getCategoryColor(job.category).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            job.category,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: _getCategoryColor(job.category),
            ),
          ),
        ),
        if (job.isUrgent) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.flash_on_rounded, size: 12, color: Colors.orange),
                const SizedBox(width: 2),
                Text(
                  'URGENT',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w700,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ),
        ],
        const Spacer(),
        Text(
          '\$${job.price.toStringAsFixed(0)}',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: scheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildJobDescription(Job job, ColorScheme scheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          job.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          job.description,
          style: TextStyle(
            color: scheme.onSurfaceVariant,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildJobDetails(Job job, ColorScheme scheme) {
    return Row(
      children: [
        _buildDetailChip(Icons.location_on_rounded, job.pickupLocation, scheme),
        const SizedBox(width: 8),
        Icon(Icons.arrow_forward_rounded, size: 16, color: scheme.onSurfaceVariant),
        const SizedBox(width: 8),
        _buildDetailChip(Icons.location_on_rounded, job.dropLocation, scheme),
      ],
    );
  }

  Widget _buildDetailChip(IconData icon, String text, ColorScheme scheme) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: scheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 14, color: scheme.onSurfaceVariant),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 12,
                  color: scheme.onSurfaceVariant,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobFooter(Job job, ColorScheme scheme) {
    return Row(
      children: [
        Icon(Icons.schedule_rounded, size: 14, color: scheme.onSurfaceVariant),
        const SizedBox(width: 4),
        Text(
          DateFormat('MMM d, HH:mm').format(job.postedTime),
          style: TextStyle(
            fontSize: 12,
            color: scheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(width: 16),
        Icon(Icons.route_rounded, size: 14, color: scheme.onSurfaceVariant),
        const SizedBox(width: 4),
        Text(
          '${job.distance} • ${job.duration}',
          style: TextStyle(
            fontSize: 12,
            color: scheme.onSurfaceVariant,
          ),
        ),
        const Spacer(),
        ElevatedButton(
          onPressed: () {
            HapticFeedback.mediumImpact();
            Vibration.vibrate(duration: 75);
            _acceptJob(job);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: scheme.primary,
            foregroundColor: scheme.onPrimary,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: const Text('Accept'),
        ),
      ],
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Ride': return Colors.blue;
      case 'Delivery': return Colors.green;
      case 'Food': return Colors.orange;
      default: return Colors.grey;
    }
  }

  void _showJobDetails(Job job) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        job.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text('Detailed job information would go here...'),
                      // Add more job details
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPostJobDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Post a Job'),
        content: const Text('Job posting form would appear here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Post Job'),
          ),
        ],
      ),
    );
  }

  void _acceptJob(Job job) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Job "${job.title}" accepted!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

class Job {
  final String id;
  final String title;
  final String description;
  final double price;
  final String distance;
  final String duration;
  final String pickupLocation;
  final String dropLocation;
  final bool isUrgent;
  final DateTime postedTime;
  final String category;
  final double clientRating;

  Job({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.distance,
    required this.duration,
    required this.pickupLocation,
    required this.dropLocation,
    required this.isUrgent,
    required this.postedTime,
    required this.category,
    required this.clientRating,
  });
}

