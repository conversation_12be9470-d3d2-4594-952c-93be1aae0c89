import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:vibration/vibration.dart';

import 'drivers_screen.dart';
import 'jobs_screen.dart';
import 'forums_screen.dart';
import 'news_screen.dart';
import 'settings_screen.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  late AnimationController _fabAnimationController;
  late AnimationController _borderRadiusAnimationController;
  late Animation<double> fabAnimation;
  late Animation<double> borderRadiusAnimation;
  late CurvedAnimation fabCurve;
  late CurvedAnimation borderRadiusCurve;

  final List<IconData> iconList = [
    Icons.directions_car_rounded,
    Icons.work_rounded,
    Icons.forum_rounded,
    Icons.newspaper_rounded,
    Icons.settings_rounded,
  ];

  final List<String> titleList = [
    'Drivers',
    'Jobs',
    'Forums',
    'News',
    'Settings',
  ];

  final List<Widget> pages = [
    const DriversScreen(),
    const JobsScreen(),
    const ForumsScreen(),
    const NewsScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _borderRadiusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    fabCurve = CurvedAnimation(
      parent: _fabAnimationController,
      curve: const Interval(0.5, 1.0, curve: Curves.fastOutSlowIn),
    );
    borderRadiusCurve = CurvedAnimation(
      parent: _borderRadiusAnimationController,
      curve: const Interval(0.5, 1.0, curve: Curves.fastOutSlowIn),
    );

    fabAnimation = Tween<double>(begin: 0, end: 1).animate(fabCurve);
    borderRadiusAnimation = Tween<double>(begin: 0, end: 1).animate(borderRadiusCurve);

    _fabAnimationController.forward();
    _borderRadiusAnimationController.forward();
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _borderRadiusAnimationController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    if (index != _currentIndex) {
      // Haptic feedback
      HapticFeedback.lightImpact();
      Vibration.vibrate(duration: 50);
      
      setState(() {
        _currentIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    
    return Scaffold(
      extendBody: true,
      appBar: AppBar(
        title: Text(
          titleList[_currentIndex],
          style: const TextStyle(fontWeight: FontWeight.w700),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                scheme.primaryContainer.withValues(alpha: 0.8),
                scheme.primary.withValues(alpha: 0.9),
              ],
            ),
          ),
        ),
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: pages,
      ),
      floatingActionButton: ScaleTransition(
        scale: fabAnimation,
        child: FloatingActionButton(
          backgroundColor: scheme.primary,
          onPressed: () {
            HapticFeedback.mediumImpact();
            Vibration.vibrate(duration: 100);
            // Quick action - could be "Add Driver" or "Post Job" based on current tab
            _showQuickActionDialog();
          },
          child: Icon(
            _getQuickActionIcon(),
            color: scheme.onPrimary,
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: AnimatedBottomNavigationBar.builder(
        itemCount: iconList.length,
        tabBuilder: (int index, bool isActive) {
          final color = isActive ? scheme.primary : scheme.onSurfaceVariant;
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                iconList[index],
                size: 24,
                color: color,
              ),
              const SizedBox(height: 4),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  titleList[index],
                  maxLines: 1,
                  style: TextStyle(
                    color: color,
                    fontSize: 11,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                  ),
                ),
              )
            ],
          );
        },
        backgroundColor: scheme.surface.withValues(alpha: 0.95),
        activeIndex: _currentIndex,
        gapLocation: GapLocation.center,
        notchSmoothness: NotchSmoothness.verySmoothEdge,
        leftCornerRadius: 24,
        rightCornerRadius: 24,
        onTap: _onTabTapped,
        hideAnimationController: _borderRadiusAnimationController,
        shadow: BoxShadow(
          offset: const Offset(0, -2),
          blurRadius: 12,
          spreadRadius: 0.5,
          color: Colors.black.withValues(alpha: 0.1),
        ),
      ),
    );
  }

  IconData _getQuickActionIcon() {
    switch (_currentIndex) {
      case 0: return Icons.person_add_rounded;
      case 1: return Icons.add_task_rounded;
      case 2: return Icons.chat_rounded;
      case 3: return Icons.article_rounded;
      case 4: return Icons.admin_panel_settings_rounded;
      default: return Icons.add_rounded;
    }
  }

  void _showQuickActionDialog() {
    final actions = [
      'Register as Driver',
      'Post New Job',
      'Start Chat',
      'Share News',
      'Partner Registration',
    ];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Quick Action'),
        content: Text('${actions[_currentIndex]} - Coming Soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

